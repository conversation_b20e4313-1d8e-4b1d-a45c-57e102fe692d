import '../widget_languages/languages.dart';

/// Default Cycle length of menstrual cycle
const defaultCycleLength = 28;

/// Default period duration of menstrual cycle
const defaultPeriodDuration = 5;

/// Default Follicular duration of menstrual cycle
const defaultFollicularDay = 6;

/// Default ovulation duration of menstrual cycle
const defaultOvulationDay = 5;

/// set how many future month consider for calender
const futureMonthCount = 12;

/// Default option of graph view
List<String> choices = <String>[
  WidgetBaseLanguage.optionsDownloadImage,
  WidgetBaseLanguage.optionsDownloadPDF,
];

const flutterPackageName = "menstrual_cycle_widget";
const drinkWaterImage = "assets/images/drink_water.png";
const sleepImage = "assets/images/sleep.png";
const temperatureImage = "assets/images/temperature.png";
const weightImage = "assets/images/weight.png";
const yogaImage = "assets/images/yoga.png";
