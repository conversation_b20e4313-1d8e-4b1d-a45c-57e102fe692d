enum MenstrualCycleTheme { basic, arcs, circle }

enum PhaseTextBoundaries { inside, outside, both, none }

enum MenstrualCycleViewType { image, text, none, circleImage, circleText }

enum BorderType { circle, rRect, rect, oval }

enum DashOffsetType { absolute, percentage }

/// Gestures available for the calendar.
enum AvailableGestures { none, verticalSwipe, horizontalSwipe, all }

/// Formats that the calendar can display.
enum CalendarFormat { month, twoWeeks, week }

enum SwipeDetectionMoment { onEnd, onUpdate }

enum WaterUnits { ml, liters, flOz, usGallon, imperialGallons, cups }

enum BodyTemperatureUnits {
  celsius,
  fahrenheit,
}

enum WeightUnits {
  kg,
  lb,
}

enum Languages { english, hindi, arabic }

enum DateFormats { dmy, ymd, mdy }
