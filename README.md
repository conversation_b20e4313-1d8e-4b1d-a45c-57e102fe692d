# Menstrual Cycle Widget

<div align="center">
<a href="https://pub.dev/packages/menstrual_cycle_widget/"><img src="https://img.shields.io/pub/v/url_launcher.svg" /></a>
<a href="https://opensource.org/licenses/MIT" target="_blank"><img src="https://img.shields.io/badge/License-MIT-yellow.svg"/></a>
<a href="https://opensource.org/licenses/Apache-2.0" target="_blank"><img src="https://badges.frapsoft.com/os/v1/open-source.svg?v=102"/></a>
<a href="https://github.com/sandipkalola/menstrual_cycle_widget/issues" target="_blank"><img alt="GitHub: sandipkalola" src="https://img.shields.io/github/issues-raw/sandipkalola/menstrual_cycle_widget?style=flat" /></a>
<img src="https://img.shields.io/github/last-commit/sandipkalola/menstrual_cycle_widget" />
<a href="https://github.com/sandipkalola"><img alt="GitHub: sandipkalola" src="https://img.shields.io/github/followers/sandipkalola?label=Follow&style=social" /></a>
<a href="https://github.com/sandipkalola/menstrual_cycle_widget"><img src="https://img.shields.io/github/stars/sandipkalola/menstrual_cycle_widget?style=social" /></a>

<a href="https://github.com/sponsors/sandipkalola"><img src="https://img.shields.io/github/sponsors/sandipkalola" /></a>

</div>

### Overview

**Menstrual Cycle Widget** is a comprehensive and customizable widget designed to help users to show
their menstrual cycles seamlessly. It is built with Flutter, allowing easy integration into any
Flutter application. This widget provides an intuitive interface, essential features, and insightful
visualizations to support users in understanding their menstrual days.

#### Note

> - **Menstrual Cycle Widget** widget is not a diagnostic tool.
> - Default cycle length is 28 days. Default period duration is 5 days.

### Features

##### 1) Cycle Tracking:

- Record the start and end dates of menstrual periods.
- Track cycle length and predict future periods and ovulation days.
- Log your daily symptoms and other notes.

##### 2) Calendar View:

- Monthly view with highlighted period days.
- Display predicted ovulation and fertile windows.
- Option to add periods log.

##### 3) Graph View:

- Body temperature graph
- Cycle trends graph
- Period cycle graph
- Cycle history graph
- Water graph
- Sleep graph
- Weight graph
- Meditation graph
- Estrogen & Progesterone Graph

##### 4) Customization:

- Theme color options to match the app’s style.
- Flexible Customization: Tailor multiple aspects of the app based on your specific requirements.
- Multi-Language Support: Offer a localized experience with support for multiple languages.
- Custom Font Support: Integrate and use custom fonts to enhance the app's aesthetics.
- Configurable Options: Adjust settings such as cycle length, period duration, user ID, and other
  preferences to fit user needs.

##### 5) Privacy & Security:

- Secure data storage with local encryption.

## Documentation

Check out the  [Documentation](https://sks-organization-5.gitbook.io/menstrual-cycle-widget) for
complete details about this widget.

## Supported Platforms

- [x] iOS
- [x] Android

### Multi-Language Support

| **English**                                                                                                                           | **Arabic**                                                                                                                            | 
|---------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------|
| <img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/language_en.jpg?raw=true" height = "200px"> | <img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/language_ar.jpg?raw=true" height = "200px"> |

### Menstrual cycle widget theme example

| **MenstrualCycleTheme.basic**                                                                                                   | **MenstrualCycleTheme.arcs**                                                                                                   | **MenstrualCycleTheme.circle**                                                                                                    |
|---------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------|
| <img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/basic.jpg?raw=true" height = "200px"> | <img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/arcs.jpg?raw=true" height = "200px"> | <img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/circle.jpg?raw=true"  height = "200px"> |

### Phase text boundaries example

|                                                                                                                                                                                                                                                                                                                                                                                                                              |
|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **PhaseTextBoundaries.inside**                                                                                                                                                                                                                                                                                                                                                                                               |
| <img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/arcs.jpg?raw=true"  height = "200px"> <img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/basic.jpg?raw=true"  height = "200px"> <img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/circle.jpg?raw=true"  height = "200px">                           |
| **PhaseTextBoundaries.outside**                                                                                                                                                                                                                                                                                                                                                                                              |
| <img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/arc_outside.jpeg?raw=true"  height = "200px"> <img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/basic_outside.jpeg?raw=true"  height = "200px"> <img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/circle_outside.jpeg?raw=true"  height = "200px"> |
| **PhaseTextBoundaries.both**                                                                                                                                                                                                                                                                                                                                                                                                 |
| <img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/arc_both.jpeg?raw=true"  height = "200px"> <img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/basic_both.jpeg?raw=true"  height = "200px"> <img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/circle_both.jpeg?raw=true"  height = "200px">          |

### viewType example

| **MenstrualCycleViewType.circleImage**                                                                                                                | **MenstrualCycleViewType.image**                                                                                                                      | 
|-------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------|
| <img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/menstrual_cycle_view_type1.png?raw=true"  height = "200px"> | <img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/menstrual_cycle_view_type2.png?raw=true"  height = "200px"> |
| **MenstrualCycleViewType.text**                                                                                                                       | **MenstrualCycleViewType.circleText**                                                                                                                 | 
| <img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/menstrual_cycle_view_type3.png?raw=true"  height = "200px"> | <img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/menstrual_cycle_view_type4.png?raw=true"  height = "200px"> |

## [Monthly calender view](https://sks-organization-5.gitbook.io/menstrual-cycle-widget/widgets/monthly-calender-view)

<img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/month_view.jpeg?raw=true" height = "400px"> 

## Estrogen & Progesterone Graph

<img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/estrogen.jpg?raw=true" height = "400px">

## [Weekly calender view](https://sks-organization-5.gitbook.io/menstrual-cycle-widget/widgets/weekly-calender-view)

<img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/week.jpeg?raw=true" height = "400px"> 

## [Body temperature graph](https://sks-organization-5.gitbook.io/menstrual-cycle-widget/graphs/body-temperature-graph)

<img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/temp_graph.jpeg?raw=true" height = "400px"> 

## [Cycle trends graph](https://sks-organization-5.gitbook.io/menstrual-cycle-widget/graphs/cycle-trends-graph)

<img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/cycle_trends.jpeg?raw=true" height = "400px"> 

## [Period cycle graph](https://sks-organization-5.gitbook.io/menstrual-cycle-widget/graphs/period-cycle-graph)

<img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/period_graph.jpeg?raw=true" height = "400px"> 

## [Cycle history graph](https://sks-organization-5.gitbook.io/menstrual-cycle-widget/graphs/cycle-history-graph)

<img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/cycle_history.jpeg?raw=true" height = "400px">

## [Water graph](https://sks-organization-5.gitbook.io/menstrual-cycle-widget/graphs/water-graph)

<img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/water_graph.jpeg?raw=true" height = "400px"> 

## [Sleep graph](https://sks-organization-5.gitbook.io/menstrual-cycle-widget/graphs/sleep-graph)

<img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/sleep_graph.jpeg?raw=true" height = "400px"> 

## [Weight graph](https://sks-organization-5.gitbook.io/menstrual-cycle-widget/graphs/weight-graph)

<img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/weight_graph.jpeg?raw=true" height = "400px"> 

## [Meditation graph](https://sks-organization-5.gitbook.io/menstrual-cycle-widget/graphs/meditation-graph)

<img src="https://github.com/sandipkalola/menstrual_cycle_widget_example/blob/main/assets/meditation_graph.jpeg?raw=true" height = "400px"> 

## Bugs or Requests

If you encounter any problems feel free to open
an [issue](https://github.com/sandipkalola/menstrual_cycle_widget/issues/new?template=bug_report.md).
If you feel the library is missing a feature, please raise
a [ticket](https://github.com/sandipkalola/menstrual_cycle_widget/issues/new?template=feature_request.md)
on GitHub.

## Github example link

https://github.com/sandipkalola/menstrual_cycle_widget_example

## Donate

> If you found this project helpful or learned something from the source code, you can show your
> appreciation by buying me a cup of ☕.
>
> - [PayPal](https://paypal.me/sandipkalola)
> - [GitHub Sponsors](https://github.com/sponsors/sandipkalola)

Thank you for your support!
