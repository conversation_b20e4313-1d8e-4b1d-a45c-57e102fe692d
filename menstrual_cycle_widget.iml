<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="android" name="Android">
      <configuration />
    </facet>
  </component>
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/build" />
      <excludeFolder url="file://$MODULE_DIR$/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/menstrual_cycle_phases_view_example/build" />
      <excludeFolder url="file://$MODULE_DIR$/menstrual_cycle_phases_view_example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/menstrual_cycle_phases_view_example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/menstrual_cycle_widget_example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/menstrual_cycle_widget_example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/menstrual_cycle_widget_example/build" />
    </content>
    <orderEntry type="jdk" jdkName="Android API 30 Platform" jdkType="Android SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Dart SDK" level="project" />
    <orderEntry type="library" name="Dart Packages" level="project" />
  </component>
</module>