[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: x86", "file_": "/Users/<USER>/Development/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON '/Users/<USER>/Downloads/menstrual_cycle_widget-master 2/android/app/.cxx/Debug/1c5q4c20/x86/android_gradle_build.json' was up-to-date", "file_": "/Users/<USER>/Development/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/Development/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]