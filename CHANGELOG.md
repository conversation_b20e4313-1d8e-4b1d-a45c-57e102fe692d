## [3.9.2] - 7 july 2025

* Bug Fixes: Resolved minor bugs to enhance overall stability and performance.


## [3.9.1] - 24 June 2025

* Bug Fixes: Resolved minor bugs to enhance overall stability and performance.
* New Feature: Redesigned Cycle History Graph for a cleaner and more modern look.


## [3.8.1] - 20 June 2025

* Bug Fixes: Resolved minor bugs to enhance overall stability and performance.
* New Feature: Added Custom function to generate detailed report for Menstrual Cycle.


## [3.7.1] - 22 May 2025

* New Feature: Added Estrogen-Progesterone Graph.


## [3.6.2] - 9 May 2025

* Improved: Enhanced UI/UX for better readability of symptoms log view


## [3.6.1] - 30 April 2025

* New Feature: Users can now create backups of their data.
* New Feature: Users can restore from previously saved backups.

## [3.5.0] - 13 March 2025

* Improved: Enhanced UI/UX for better readability of pregnancy-related details
* Enhancements: Added new parameter `imageHeight`,`imageWidth`,`imageUrl` into PregnancyView

## [3.4.2] - 27 Feb 2025

* New Feature: Pregnancy view to display the current trimester.
* Improved: Enhanced UI/UX for better readability of pregnancy-related details
* Enhancements: Added new JSON parameter `pregnancy_matrix` into summary

## [3.3.2] - 11 Feb 2025

* Feature: Added a callback for day click in MenstrualCyclePhaseView, allowing users to handle day
  selection events.

## [3.2.2] - 8 Feb 2025

* Enhancements: Added new JSON parameter `predicted_symptoms_pattern_tomorrow` into summary
* New Feature: Implemented function to retrieve symptoms pattern based on the current cycle day.

## [3.0.0] - 16 Jan 2025

* Enhancements: Added multi-language support
* Enhancements: Added custom font support.

## [2.4.0] - 19 Dec 2024

* Enhancements: Added pagination to the monthly calendar view for improved performance.
* Enhancements: Changed the default color of the menstrual view for a better user experience.
* Bug Fixes: Resolved minor bugs to enhance overall stability and performance.

## [2.3.4] - 12 Dec 2024

* Custom function to add dummy data for testing purposes.
* Custom function to verify if the graph contains data

## [2.3.1] - 10 Dec 2024

* Added Custom function

## [2.1.2] - 4 Dec 2024

* Added new params 'themeColor' into MenstrualLogPeriodView
* Ui enhancement of MenstrualLogPeriodView
* Fixed minor bugs

## [2.1.0] - 29 Nov 2024

* Fixed minor bugs
* Added functionality to update previous symptom log reports

## [2.0.2] - 23 Nov 2024

* Added Graph
* Minor bug fixed

## [1.1.0] - 13 July 2024

* Added new params 'isAutoSetData' into MenstrualCyclePhaseView
* Added new table for store current user details
* Show dynamic messages into MenstrualCyclePhaseView
* Update README.md File description.
* Minor bug fixed

## [1.0.0] - 06 July 2024

* Added Calender view
* Added Monthly calender view
* Edit periods dates
* Minor bug fixed

## [0.1.5] - 19 June 2024

* Added "viewType" of 'Menstrual Cycle Widget'

## [0.1.4] - 18 June 2024

* Added new params
* added functionality of store store user's symptoms
* added encryption functionality for storing user's data
* Update README File description.

## [0.1.3] - 16 June 2024

* Added new params
* Update README File description.

## [0.0.1] - 4 June 2024

* initial release.